package com.exchangegw.services.publisher;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

import com.exchangegw.model.global.PublishEvent;
import org.zeromq.ZMQ;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.ObjectMapper;

public class ZmqPublisherWorker implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(ZmqPublisherWorker.class);
    private final BlockingQueue<PublishEvent> queue = new LinkedBlockingDeque<PublishEvent>(100000);
    private final ObjectMapper mapper = new ObjectMapper();
    private final String serverAddress;

    private ZMQ.Context context;
    private ZMQ.Socket sendSocket;
    private ZMQ.Socket pingSocket;

    private volatile boolean running = true;
    private Thread pingThread;
    private MultiZmqPublisher multiZmqPublisher;

    public ZmqPublisherWorker(String _serverAddress) {
        this.serverAddress = _serverAddress;
        initSockets();
//       startPingThread();
    }

    public void setMultiZmqPublisher(MultiZmqPublisher multi) {
        this.multiZmqPublisher = multi;
    }

    private void initSockets() {
        if (context != null) {
            context.term();
        }
        context = ZMQ.context(1);

        // Socket gửi dữ liệu
        if (sendSocket != null) {
            sendSocket.close();
        }
        sendSocket = context.socket(ZMQ.DEALER);
        sendSocket.setLinger(0);
        sendSocket.connect(serverAddress);

        // Socket gửi ping
        if (pingSocket != null) {
            pingSocket.close();
        }
        pingSocket = context.socket(ZMQ.DEALER);
        pingSocket.setLinger(0);
        pingSocket.connect(serverAddress);
    }

    public void send(PublishEvent data) {
        if (!running || data == null) return;
        try {
            String json = mapper.writeValueAsString(data);
            if (json.contains("\0") || json.trim().isEmpty()) return;
            queue.offer(data);
        } catch (Exception e) {
            logger.error("Error serializing data to JSON: {}", e.getMessage(), e);
        }
    }

    public void shutdown() {
        running = false;
        if (pingThread != null) pingThread.interrupt();
    }

    private synchronized void reconnect() {
        try {
            if (sendSocket != null) sendSocket.close();
            if (pingSocket != null) pingSocket.close();
            if (context != null) context.term();
        } catch (Exception ignored) {}

        initSockets();
        if (multiZmqPublisher != null) {
            multiZmqPublisher.resendCacheToServer(this);
        }
        logger.info("Reconnected to server: {}", serverAddress);
    }

    private void startPingThread() {
        pingThread = new Thread(() -> {
            int missedPongCount = 0;
            final int MAX_MISSED_PONGS = 3;

            ZMQ.Poller poller = context.poller(1);
            poller.register(pingSocket, ZMQ.Poller.POLLIN);

            while (running) {
                try {
                    String pingJson = "{\"type\":\"ping\"}";
                    boolean sent = pingSocket.send(pingJson.getBytes(StandardCharsets.UTF_8), 0);

                    if (!sent) {
                        logger.error("Ping not sent to server: {}", serverAddress);
                        missedPongCount++;
                    } else {
                        int polled = poller.poll(1000); // 1s timeout

                        if (polled > 0 && poller.pollin(0)) {
                            byte[] identity = pingSocket.recv(0); // Identity
                            byte[] empty = pingSocket.recv(0);     // Empty
                            byte[] data = pingSocket.recv(0);      // Actual data

                            String pong = new String(data, StandardCharsets.UTF_8);
                            if (pong.contains("pong")) {
                                logger.debug("Received PONG from server");
                                missedPongCount = 0;
                            } else {
                                logger.error("Unexpected response: {}", pong);
                                missedPongCount++;
                            }
                        } else {
                            logger.error("PONG not received in time");
                            missedPongCount++;
                        }
                    }

                    if (missedPongCount >= MAX_MISSED_PONGS) {
                        logger.error("Connection to server lost. Reconnecting...");
                        reconnect();
                        missedPongCount = 0;
                    }

                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    logger.error("[PingThread] Exception: {}", e.getMessage(), e);
                    reconnect();
                }
            }
        });

        pingThread.setDaemon(true);
        pingThread.start();
    }

    @Override
    public void run() {
        while (running && !Thread.currentThread().isInterrupted()) {
            try {
                PublishEvent data = queue.poll(1, TimeUnit.MILLISECONDS);
                if(data == null ) continue;
                String json = mapper.writeValueAsString(data);
                if (json.contains("\0") || json.trim().isEmpty()) continue;
                boolean sent = sendSocket.send(json.getBytes(StandardCharsets.UTF_8), ZMQ.DONTWAIT);
                if (!sent) {
                    logger.error("Message not sent to server: {}, JSON: {}", serverAddress, json);
                } else {
                     logger.debug("Sent message: {}", json);
                }
            } catch (Exception e) {
                logger.error("Send error to server: {} : {}", serverAddress, e.getMessage(), e);
            }
        }

        if (sendSocket != null) sendSocket.close();
        if (pingSocket != null) pingSocket.close();
        if (context != null) context.term();
    }
}
